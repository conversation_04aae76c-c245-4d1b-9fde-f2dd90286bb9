'use client';

import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';

if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export default function Home() {
  const heroRef = useRef(null);
  const navRef = useRef(null);
  const sectionsRef = useRef([]);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero animation
      gsap.fromTo(
        '.hero-title',
        { y: 100, opacity: 0 },
        { y: 0, opacity: 1, duration: 1.2, ease: 'power3.out', delay: 0.5 }
      );

      gsap.fromTo(
        '.hero-subtitle',
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: 'power3.out', delay: 0.8 }
      );

      gsap.fromTo(
        '.hero-description',
        { y: 30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: 'power3.out', delay: 1.1 }
      );

      // Navigation animation
      gsap.fromTo(
        '.nav-item',
        { y: -30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6, ease: 'power3.out', stagger: 0.1, delay: 0.2 }
      );

      // Floating animation for hero elements
      gsap.to('.floating-1', {
        y: -20,
        duration: 3,
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true
      });

      gsap.to('.floating-2', {
        y: -15,
        duration: 2.5,
        ease: 'power2.inOut',
        repeat: -1,
        yoyo: true,
        delay: 0.5
      });

      // Scroll-triggered animations
      gsap.utils.toArray('.fade-in-up').forEach((element, index) => {
        gsap.fromTo(
          element,
          { y: 80, opacity: 0 },
          {
            y: 0,
            opacity: 1,
            duration: 1,
            ease: 'power3.out',
            scrollTrigger: {
              trigger: element,
              start: 'top 80%',
              end: 'bottom 20%',
              toggleActions: 'play none none reverse'
            }
          }
        );
      });

      // Service cards animation
      gsap.utils.toArray('.service-card').forEach((card, index) => {
        gsap.fromTo(
          card,
          { y: 60, opacity: 0, scale: 0.9 },
          {
            y: 0,
            opacity: 1,
            scale: 1,
            duration: 0.8,
            ease: 'power3.out',
            scrollTrigger: {
              trigger: card,
              start: 'top 85%',
              end: 'bottom 15%',
              toggleActions: 'play none none reverse'
            },
            delay: index * 0.1
          }
        );
      });

      // Solution cards stagger animation
      gsap.utils.toArray('.solution-card').forEach((card, index) => {
        gsap.fromTo(
          card,
          { x: index % 2 === 0 ? -60 : 60, opacity: 0 },
          {
            x: 0,
            opacity: 1,
            duration: 1,
            ease: 'power3.out',
            scrollTrigger: {
              trigger: card,
              start: 'top 85%',
              end: 'bottom 15%',
              toggleActions: 'play none none reverse'
            },
            delay: index * 0.15
          }
        );
      });

      // Parallax effect for background elements
      gsap.utils.toArray('.parallax-bg').forEach((element) => {
        gsap.to(element, {
          yPercent: -50,
          ease: 'none',
          scrollTrigger: {
            trigger: element,
            start: 'top bottom',
            end: 'bottom top',
            scrub: true
          }
        });
      });

    }, heroRef);

    return () => ctx.revert();
  }, []);

  return (
    <div className="overflow-x-hidden">
      {/* Navigation */}
      <nav ref={navRef} className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center nav-item">
              <div className="flex-shrink-0 flex items-center">
                <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">D</span>
                </div>
                <span className="ml-2 text-xl font-bold text-gray-900">.tech</span>
              </div>
            </div>
            
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                <a href="#services" className="nav-item text-gray-700 hover:text-primary px-3 py-2 text-sm font-medium transition-colors">Services</a>
                <a href="#solutions" className="nav-item text-gray-700 hover:text-primary px-3 py-2 text-sm font-medium transition-colors">Solutions</a>
                <a href="#about" className="nav-item text-gray-700 hover:text-primary px-3 py-2 text-sm font-medium transition-colors">About</a>
                <a href="#contact" className="nav-item bg-primary text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-primary-dark transition-colors">Contact</a>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section ref={heroRef} className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="parallax-bg absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full blur-3xl floating-1"></div>
          <div className="parallax-bg absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-br from-secondary/20 to-primary/20 rounded-full blur-3xl floating-2"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="hero-title text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">
            Connecting the
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary">
              Enterprise
            </span>
          </h1>
          
          <p className="hero-subtitle text-xl md:text-2xl text-gray-600 mb-8 max-w-2xl mx-auto">
            One Great Workflow at a Time.
          </p>
          
          <p className="hero-description text-lg text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
            Focused on driving transformative business outcomes across the enterprise with the ServiceNow Platform.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-primary text-white px-8 py-4 rounded-full text-lg font-medium hover:bg-primary-dark transition-all duration-300 transform hover:scale-105">
              Get Started
            </button>
            <button className="border-2 border-primary text-primary px-8 py-4 rounded-full text-lg font-medium hover:bg-primary hover:text-white transition-all duration-300">
              Learn More
            </button>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="fade-in-up text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              At the Crossroads of Automation,
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary">
                Integration, and Innovation
              </span>
            </h2>
            <p className="fade-in-up text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              DESBY Technologies is a management, technology, and strategy consulting firm comprised of innovative problem solvers. Powered by a team that believes in creating value for organizations through purpose-built workflows with ServiceNow, the DESBY team takes a holistic, agile, secure, and customer-centered approach to digital transformation, advancing our clients' business outcomes.
            </p>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="fade-in-up text-4xl md:text-5xl font-bold text-gray-900 mb-6">Our Services</h2>
            <p className="fade-in-up text-xl text-gray-600 max-w-3xl mx-auto">
              Through a culture of excellence established from the company's inception, our team strives to deliver quality services within each of our client engagements.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Consulting & Advisory Services",
                description: "Our team of experts deliver strategic guidance to plan for implementations, solve architectural challenges, and create scalable solutions that maximize ROI of ServiceNow.",
                icon: "💡"
              },
              {
                title: "Implementations",
                description: "Our certified experts work with customers to ensure a collaborative implementation from start to finish. We look at the bigger picture during and throughout the project to deliver outcomes on time and within budget.",
                icon: "🚀"
              },
              {
                title: "Integrations",
                description: "We help our customers to connect systems and data across the enterprise to drive efficiencies and productivity at scale, while removing swivel chairing across systems.",
                icon: "🔗"
              },
              {
                title: "Managed Services",
                description: "DESBY Managed Services delivers highly flexible, outcomes-based support services to administer, develop, maintain, upgrade, and scale our customers' ServiceNow investment.",
                icon: "⚙️"
              },
              {
                title: "Remote Administration",
                description: "Delivering onshore and offshore technical support as a partial block of hours per month for our Customers that need part-time support.",
                icon: "🔧"
              },
              {
                title: "Training",
                description: "The DESBY Team delivers customer tailored training services and adoption activities to increase ROI of ServiceNow. We partner closely with you to ensure adoption and engagement of these new technologies.",
                icon: "📚"
              }
            ].map((service, index) => (
              <div key={index} className="service-card bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="text-4xl mb-4">{service.icon}</div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{service.title}</h3>
                <p className="text-gray-600 leading-relaxed">{service.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Solutions Section */}
      <section id="solutions" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="fade-in-up text-4xl md:text-5xl font-bold text-gray-900 mb-6">Our Solutions</h2>
            <p className="fade-in-up text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Along with using world class solutions on the ServiceNow Platform, we leverage deep expertise in digital transformation, a consultative approach to business solutions, and a robust delivery excellence program to deliver transformational digital business outcomes.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Customer Service Management",
                description: "Increase customer satisfaction and loyalty by creating a modernized experience for customers, citizens, and constituents."
              },
              {
                title: "IT Service Management",
                description: "Delivering best-in-class IT support services to end users to deliver faster time-to-fulfillment of IT requests and resolution of IT issues."
              },
              {
                title: "IT Operations Management",
                description: "Increase productivity across the enterprise by proactively identifying and managing IT Ops issues before they occur."
              },
              {
                title: "Employee Experience",
                description: "Increase productivity and employee engagement by streamlining the employee experience with intelligent request workflows and a modern portal."
              },
              {
                title: "Custom Applications",
                description: "Build a best-in-class modernized experience for end users through low-code custom applications to address their specific business needs."
              },
              {
                title: "Strategic Portfolio Management",
                description: "Plan, track, prioritize, and deliver valuable business outcomes aligned to your needs. Track investments and spend to gain better insight."
              },
              {
                title: "Asset Management",
                description: "Unify your hardware, software, and cloud asset tracking into a centralized CMDB to optimize costs and reduce risks."
              },
              {
                title: "Human Resources",
                description: "Happy people are productive people. Streamline the employee service experience and HR processes with intelligent workflows."
              },
              {
                title: "Security and Risk",
                description: "Proactively manage threats and vulnerabilities with accelerated incident response times. Connect your cyber resilience pointed solutions."
              }
            ].map((solution, index) => (
              <div key={index} className="solution-card bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200 hover:border-primary/30 transition-all duration-300">
                <h3 className="text-lg font-bold text-gray-900 mb-3">{solution.title}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">{solution.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-primary to-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="fade-in-up text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Transform Your Business?
          </h2>
          <p className="fade-in-up text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Let's discuss how we can help you connect your enterprise with powerful ServiceNow solutions.
          </p>
          <button className="fade-in-up bg-white text-primary px-8 py-4 rounded-full text-lg font-medium hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
            Contact Us Today
          </button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">D</span>
                </div>
                <span className="ml-2 text-xl font-bold">.tech</span>
              </div>
              <p className="text-gray-400">
                Connecting the Enterprise. One Great Workflow at a Time.
              </p>
            </div>
            
            <div>
              <h4 className="font-bold mb-4">Services</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">Consulting</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Implementations</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Integrations</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Managed Services</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-bold mb-4">Solutions</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">ITSM</a></li>
                <li><a href="#" className="hover:text-white transition-colors">CSM</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Employee Experience</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Custom Applications</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-bold mb-4">Contact</h4>
              <ul className="space-y-2 text-gray-400">
                <li><EMAIL></li>
                <li>+1 (555) 123-4567</li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 DESBY Technologies. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}