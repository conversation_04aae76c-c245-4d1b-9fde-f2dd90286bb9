@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables based on brand colors */
:root {
  --primary: #243665;
  --primary-dark: #1a2a4a;
  --secondary: #8bd88d;
  --accent: #1b91a7;
  --contrast: #fc9e4f;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

/* Custom Tailwind Classes */
@layer components {
  .btn-primary {
    @apply bg-primary text-white px-6 py-3 rounded-full font-medium transition-all duration-300 hover:bg-primary-dark hover:shadow-lg transform hover:scale-105;
  }
  
  .btn-secondary {
    @apply border-2 border-primary text-primary px-6 py-3 rounded-full font-medium transition-all duration-300 hover:bg-primary hover:text-white;
  }
  
  .card {
    @apply bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300;
  }
  
  .section-padding {
    @apply py-16 md:py-20 lg:py-24;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Custom Animation Classes */
@layer utilities {
  .text-gradient {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary;
  }
  
  .bg-gradient-primary {
    @apply bg-gradient-to-br from-primary to-secondary;
  }
  
  .shadow-glow {
    box-shadow: 0 0 30px rgba(36, 54, 101, 0.15);
  }
  
  .shadow-glow-secondary {
    box-shadow: 0 0 30px rgba(139, 216, 141, 0.15);
  }
  
  .backdrop-blur-glass {
    backdrop-filter: blur(12px) saturate(120%);
    background-color: rgba(255, 255, 255, 0.8);
  }
  
  .border-gradient {
    border-image: linear-gradient(45deg, var(--primary), var(--secondary)) 1;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--primary), var(--secondary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, var(--primary-dark), var(--secondary));
}

/* Improved Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2rem);
}

p {
  line-height: 1.7;
  color: var(--text-secondary);
}

/* Link Styles */
a {
  color: var(--primary);
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--accent);
}

/* Button Hover Effects */
.btn-hover-effect {
  position: relative;
  overflow: hidden;
}

.btn-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-hover-effect:hover::before {
  left: 100%;
}

/* Card Hover Effects */
.card-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Animated Gradients */
.animated-gradient {
  background: linear-gradient(-45deg, var(--primary), var(--secondary), var(--accent), var(--primary));
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Floating Animation */
.floating {
  animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Pulse Animation */
.pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Loading Animations */
.loading-dots {
  display: inline-flex;
  align-items: center;
}

.loading-dots::after {
  content: '';
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: currentColor;
  animation: loading-dots 1.5s linear infinite;
  margin-left: 8px;
}

@keyframes loading-dots {
  0%, 20% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* Responsive Design Improvements */
@media (max-width: 768px) {
  .hero-title {
    font-size: 3rem;
    line-height: 1.1;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
  }
  
  .section-padding {
    @apply py-12;
  }
  
  .service-card,
  .solution-card {
    margin-bottom: 1rem;
  }
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles */
*:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --primary: #000000;
    --secondary: #ffffff;
    --text-primary: #000000;
    --text-secondary: #000000;
  }
}

/* Selection Styles */
::selection {
  background: var(--primary);
  color: white;
}

::-moz-selection {
  background: var(--primary);
  color: white;
}

/* Custom Utilities for Tailwind */
.text-primary {
  color: var(--primary);
}

.text-secondary {
  color: var(--secondary);
}

.bg-primary {
  background-color: var(--primary);
}

.bg-secondary {
  background-color: var(--secondary);
}

.border-primary {
  border-color: var(--primary);
}

.border-secondary {
  border-color: var(--secondary);
}

.hover\:bg-primary-dark:hover {
  background-color: var(--primary-dark);
}

/* Smooth Scrolling for Safari */
@supports (-webkit-overflow-scrolling: touch) {
  html {
    -webkit-overflow-scrolling: touch;
  }
}